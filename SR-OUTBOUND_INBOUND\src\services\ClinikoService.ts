// // src/services/ClinikoService.ts
// import { makeClinikoClient, ClinikoConfig } from '../modules/cliniko';
// import { AppointmentService, Appointment } from './AppointmentService';

// export class ClinikoService implements AppointmentService {
//   private client;

//   constructor(private crmConfig: ClinikoConfig) {
//     this.client = makeClinikoClient(crmConfig);
//   }

//   async getAppointments(start: string, end: string): Promise<Appointment[]> {
//     const raws = await this.client.getAppointmentsRaw(start, end);
//     const out: Appointment[] = [];

//     for (const a of raws) {
//       const fullName = a.patient_name || 'Valued patient';

//       // fetch phone
//       let phone = '';
//       if (a.patient?.links?.self) {
//         const pd = await this.client.fetchResource<{
//           patient_phone_numbers?: { number?: string; normalized_number?: string }[];
//         }>(a.patient.links.self);
//         const pnums = pd.patient_phone_numbers || [];
//         phone = pnums[0]?.number || pnums[0]?.normalized_number || '';
//       }
//       if (!phone.startsWith('+')) continue;

//       // fetch practitioner name + id
//       let practitioner = 'your doctor';
//       let practitioner_id = '';
//       if (a.practitioner?.links?.self) {
//         const url = a.practitioner.links.self;
//         practitioner_id = url.split('/').pop() || '';
//         const pr = await this.client.fetchResource<{
//           first_name?: string;
//           last_name?: string;
//           name?: string;
//         }>(url);
//         practitioner =
//           [pr.first_name, pr.last_name].filter(Boolean).join(' ') ||
//           pr.name ||
//           practitioner;
//       }

//       const startsAt = a.starts_at || a.start_time || '';

//       const business_id = a.business?.links?.self?.split('/').pop() || '';
//       if (!business_id) continue;

//       out.push({
//         id: String(a.id),
//         patient: { id: String(a.patient.links.self), fullName, phone },
//         practitioner,
//         practitioner_id,
//         startsAt,
//         business_id,
//       });
//     }

//     console.log('Mapped appointments:', JSON.stringify(out, null, 2));
//     return out;
//   }

//   async reschedule(appointmentId: string, newTime: string, endTime: string): Promise<void> {
//     await this.client.rescheduleAppointment(appointmentId, { starts_at: newTime, ends_at: endTime });
//   }

//   async cancel(appointmentId: string): Promise<void> {
//     await this.client.cancelAppointment(appointmentId, {
//       cancellation_note: 'Cancelled by patient',
//       cancellation_reason: 50,
//     });
//   }

//   async getAvailableSlots(
//     practitioner_id: string,
//     from: string,
//     to: string,
//     business_id: string,
//     appointment_type_id: string
//   ): Promise<string[]> {
//     // Use the appointment_type_id from the request if provided, else fallback to config
//     const typeId = appointment_type_id || this.crmConfig.appointment_type_id;
//     return this.client.getAvailableSlots({
//       business_id,
//       practitioner_id,
//       appointment_type_id: typeId,
//       from,
//       to,
//     });
//   }
// }
// import { makeClinikoClient, ClinikoConfig } from '../modules/cliniko';
// import { AppointmentService } from './AppointmentService';

// export class ClinikoService implements AppointmentService {
//   private client = makeClinikoClient(this.crmConfig);

//   constructor(private crmConfig: ClinikoConfig) {}

//   async getAppointments(startIso: string, endIso: string) {
//     return this.client.getAppointmentsRaw(startIso, endIso).then(raws => {
//       // same mapping logic as before
//       // ...
//       return raws.map(a => ({
//         id: String(a.id),
//         patient: { id: '', fullName: a.patient_name, phone: '' },
//         startsAt: a.starts_at,
//         practitioner: '',
//         practitioner_id: '',
//         business_id: '',
//       }));
//     });
//   }
// }
