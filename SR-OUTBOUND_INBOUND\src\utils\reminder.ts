// // src/utils/reminders.ts
// import { parseISO, format, isValid } from 'date-fns';
// import { toZonedTime } from 'date-fns-tz';

// const DEFAULT_TZ = process.env.TZ || 'Australia/Sydney';

// /**
//  * Safely formats a date string into a natural, human-friendly format,
//  * like "Wednesday, July 17th at 9:00AM"
//  */
// export const safeFormatDate = (isoString: string, timeZone = DEFAULT_TZ): string => {
//   try {
//     const parsed = parseISO(isoString);
//     if (!isValid(parsed)) {
//       console.warn(`[WARN] Invalid ISO date: "${isoString}"`);
//       return 'a valid upcoming date';
//     }

//     const zoned = toZonedTime(parsed, timeZone);
//     return format(zoned, "EEEE, MMMM do 'at' h:mma");
//   } catch (err) {
//     console.error(`[ERROR] Failed to format date "${isoString}":`, err);
//     return 'a valid upcoming date';
//   }
// };

// /**
//  * Generates a human-friendly spoken reminder message.
//  * Customizable if needed in future.
//  */
// export const formatReminderMessage = ({
//   patient,
//   practitionerName,
//   appointmentDate,
//   clinicName,
// }: {
//   patient: { fullName: string };
//   practitionerName: string;
//   appointmentDate: string;
//   clinicName?: string;
// }): string => {
//   const formattedDate = safeFormatDate(appointmentDate);
//   const clinic = clinicName || 'your clinic';

//   return `Hello! This is Sarah from ${clinic}. Just a reminder of your appointment with Dr. ${practitionerName} on ${formattedDate}. Please let us know at least two hours in advance if you need to reschedule. Thank you!`;
// };
import { parseISO, format, isValid } from 'date-fns';
import { toZonedTime } from 'date-fns-tz';

export const safeFormatDate = (iso: string, tz: string): string => {
  const d = parseISO(iso);
  if (!isValid(d)) return 'a valid upcoming date';
  return format(toZonedTime(d, tz), "EEEE, MMMM do 'at' h:mma");
};
