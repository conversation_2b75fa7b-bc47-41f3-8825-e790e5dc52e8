// // src/services/ServiceFactory.ts
// // import { ClinikoService } from './ClinikoService';
// import { GenieService } from './GenieService';
// // import type { ClinikoConfig } from '../modules/cliniko';
// import type { ClientConfigDoc } from '../models/ClientConfig';
// import type { AppointmentService } from './AppointmentService';

// export function makeService(cfg: ClientConfigDoc): AppointmentService {
//   switch (cfg.crm) {
//     case 'cliniko': {
//       // Destructure your Cliniko credentials + ids from the stored config:
//       const {
//         baseUrl,
//         apiKey,
//         businessId,
//         appointmentTypeId,
//       } = cfg.crmConfig as {
//         baseUrl: string;
//         apiKey: string;
//         businessId: string;
//         appointmentTypeId: string;
//       };

//       // Build the full ClinikoConfig
//       // const clinCfg: ClinikoConfig = {
//       //   baseUrl,
//       //   apiKey,
//       //   business_id: businessId,
//       //   appointment_type_id: appointmentTypeId,
//       // };

//       // return new ClinikoService(clinCfg);
//     }

//     case 'genie': {
//       // GenieService still takes (baseUrl, token)
//       const { baseUrl, token } = cfg.crmConfig as {
//         baseUrl: string;
//         token: string;
//       };
//       return new GenieService(baseUrl, token);
//     }

//     default:
//       throw new Error(`Unsupported CRM: ${cfg.crm}`);
//   }
// }
import { ClientConfigDoc } from '../models/ClientConfig';
import { AppointmentService } from './AppointmentService';
import { ApiProxyService } from './ApiProxyService';

export function makeService(cfg: ClientConfigDoc): AppointmentService {
  return new ApiProxyService((cfg._id as string).toString());
}