import axios from 'axios';
import { AppointmentService } from './AppointmentService';

export class ApiProxyService implements AppointmentService {
  private base = process.env.SMART_RECEPTION_API_BASE_URL!;

  constructor(private clientId: string) {}

  async reschedule(appointmentId: string, newTime: string, endTime: string) {
    await axios.post(`${this.base}/reschedule_appointment`, {
      clientId: this.clientId,
      appointment_id: appointmentId,
      new_appointment_starts_at: newTime,
      new_appointment_ends_at: endTime,
    });
  }

  async cancel(appointmentId: string) {
    await axios.post(`${this.base}/cancel_appointment`, {
      clientId: this.clientId,
      appointment_id: appointmentId,
      cancellation_reason: 50,
      cancellation_note: 'Cancelled by patient',
    });
  }

  async getAvailableSlots(
    practitioner_id: string,
    from: string,
    to: string,
    business_id: string,
    appointment_type_id: string
  ) {
    const res = await axios.post(`${this.base}/get_available_slots`, {
      clientId: this.clientId,
      practitioner_id,
      from_date: from,
      to_date: to,
      business_id,
      appointment_type_id,
    });
    return res.data.available_slots as string[];
  }

  async getAppointments() {
    throw new Error('Not supported by ApiProxyService');
  }
}
