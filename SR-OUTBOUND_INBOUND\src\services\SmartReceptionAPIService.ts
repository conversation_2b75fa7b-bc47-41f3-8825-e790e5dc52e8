// import axios from "axios";
// import { Appointment, AppointmentService } from "./AppointmentService";

// const API_BASE = process.env.SMART_RECEPTION_API_BASE_URL!;

// export class SmartReceptionAPIService implements AppointmentService {
//   constructor(private clientId: string) {}
//     getAppointments(windowStart: string, windowEnd: string): Promise<Appointment[]> {
//         throw new Error("Method not implemented.");
//     }

//   async reschedule(appointmentId: string, newTime: string, endTime: string): Promise<void> {
//     await axios.post(`${API_BASE}/reschedule_appointment`, {
//       clientId: this.clientId,
//       appointment_id: appointmentId,
//       new_appointment_starts_at: newTime,
//       new_appointment_ends_at: endTime,
//     });
//   }

//   async cancel(appointmentId: string): Promise<void> {
//     await axios.post(`${API_BASE}/cancel_appointment`, {
//       clientId: this.clientId,
//       appointment_id: appointmentId,
//       cancellation_reason: 50,
//       cancellation_note: 'Cancelled by patient',
//     });
//   }

//   async getAvailableSlots(
//     practitioner_id: string,
//     from: string,
//     to: string,
//     business_id: string,
//     appointment_type_id: string
//   ): Promise<string[]> {
//     const res = await axios.post(`${API_BASE}/get_available_slots`, {
//       clientId: this.clientId,
//       practitioner_id,
//       from_date: from,
//       to_date: to,
//       business_id,
//       appointment_type_id,
//     });
//     return res.data.available_slots;
//   }
// }
