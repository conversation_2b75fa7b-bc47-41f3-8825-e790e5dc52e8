// import { toZonedTime } from 'date-fns-tz';
// import { ClientConfig } from '../models/ClientConfig';
// import { makeService } from '../services/ServiceFactory';
// import { sendReminders } from '../controllers/ReminderController';

// export async function runClientByName(
//   clientName: string,
//   clinicTz: string,
//   isTest = false
// ) {
//   const cfg = await ClientConfig.findOne({ name: clientName, enabled: true });
//   if (!cfg) return;

//   const now = new Date();
//   const hoursBefore = cfg.reminderHoursBefore;
//   const windowMinutes = 1440; // 24 hour window

//   // Simulate the window for 24h ahead (same as testService.ts)
//   const target = new Date(now.getTime() + hoursBefore * 3600_000);
//   const windowStart = new Date(target.getTime() - (windowMinutes / 2) * 60_000);
//   const windowEnd = new Date(target.getTime() + (windowMinutes / 2) * 60_000);

//   const svc = makeService(cfg);
//   const results = await sendReminders(
//     svc,
//     cfg.retellAgentId,
//     cfg.twilioFromNumber,
//     cfg.name,
//     clinicTz,
//     windowStart,
//     windowEnd,
//     (cfg._id as string).toString()
//   );
//   console.log(`✅ Completed for ${cfg.name}:`, results);
// }


import { ClientConfig } from '../models/ClientConfig';
import { sendReminders } from '../controllers/ReminderController';
import { fetchAppointmentsForReminder } from '../modules/appointment_fetcher';

/**
 * Runs outbound reminder workflow for a given client.
 * - Loads client config from MongoDB
 * - Calculates reminder window
 * - Uses appointment service abstraction to fetch appointments
 * - Triggers Retell reminders for each appointment
 */
export async function runClientByName(name: string, tz: string) {
  const cfg = await ClientConfig.findOne({ name, enabled: true });
  if (!cfg) return;

  const now = new Date();
  const hoursBefore = cfg.reminderHoursBefore;
  const windowMinutes = 1440; // 24 hour window

  // Simulate the window for 24h ahead (same as testService.ts)
  const target = new Date(now.getTime() + hoursBefore * 3600_000);
  const windowStart = new Date(target.getTime() - (windowMinutes / 2) * 60_000);
  const windowEnd = new Date(target.getTime() + (windowMinutes / 2) * 60_000);

  const appointments = await fetchAppointmentsForReminder((cfg._id as string).toString(), windowStart, windowEnd);
await sendReminders(
  appointments,
  cfg.retellAgentId,
  cfg.twilioFromNumber,
  cfg.name,
  tz,
  windowStart,
  windowEnd,
  (cfg._id as string).toString()
);
  console.log(`✅ Completed for ${cfg.name}:`);
}