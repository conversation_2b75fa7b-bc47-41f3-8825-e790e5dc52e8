// import { Appointment } from '../controllers/ReminderController';

export interface AppointmentService {
  reschedule?(appointmentId: string, newTime: string, endTime: string): Promise<void>;
  cancel?(appointmentId: string): Promise<void>;
  getAvailableSlots?(
    practitioner_id: string,
    from: string,
    to: string,
    business_id: string,
    appointment_type_id: string
  ): Promise<string[]>;
}