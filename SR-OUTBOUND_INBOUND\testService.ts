// import mongoose from 'mongoose';
// import { ClientConfig } from './src/models/ClientConfig';
// import { makeService } from './src/services/ServiceFactory';
// import { sendReminders } from './src/controllers/ReminderController';

// (async () => {
//   await mongoose.connect(process.env.MONGODB_URI!);

//   // Test for both clients
//   const clients = await ClientConfig.find({ enabled: true });
//   for (const cfg of clients) {
//     const svc = makeService(cfg);

//     // Simulate the window for 24h or 48h ahead
//     const now = new Date();
//     const hoursBefore = cfg.reminderHoursBefore;
//     const windowMinutes = 1440; 

//     const target = new Date(now.getTime() + hoursBefore * 3600_000);
//     const windowStart = new Date(target.getTime() - (windowMinutes / 2) * 60_000);
//     const windowEnd = new Date(target.getTime() + (windowMinutes / 2) * 60_000);

//     console.log(`Testing reminders for ${cfg.name} for window:`, windowStart, windowEnd);

//     const results = await sendReminders(
//       svc,
//       cfg.retellAgentId,
//       cfg.twilioFromNumber,
//       cfg.name,
//       process.env.TZ || 'Australia/Sydney',
//       windowStart,
//       windowEnd,
//       (cfg._id as string).toString()
//     );
//     console.log(`Results for ${cfg.name}:`, results);
//   }

//   process.exit(0);
// })();
