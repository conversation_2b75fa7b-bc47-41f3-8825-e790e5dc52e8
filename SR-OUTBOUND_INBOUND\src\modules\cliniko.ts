// import axios from 'axios';
// import qs from 'qs';

// export interface ClinikoConfig {
//   baseUrl: string;
//   apiKey: string;
//   business_id: string;
//   appointment_type_id: string;
// }

// export function makeClinikoClient(cfg: ClinikoConfig) {
//   const { baseUrl, apiKey } = cfg;
//   const auth = Buffer.from(apiKey + ':').toString('base64');

//   const instance = axios.create({
//     baseURL: baseUrl,
//     timeout: 15000, // 15s timeout for all requests
//     paramsSerializer: params =>
//       qs.stringify(params, { arrayFormat: 'brackets' }),
//     headers: {
//       Authorization: `Basic ${auth}`,
//       'User-Agent': 'SmartReceptionOutbound',
//       Accept: 'application/json',
//     },
//   });

//   return {
//     async getAppointmentsRaw(start: string, end: string) {
//       const params = {
//         per_page: 50,
//         'q': [
//           `starts_at:>${start}`,
//           `starts_at:<${end}`,
//         ],
//       };
//       try {
//         console.log('[ClinikoClient] GET', baseUrl + '/individual_appointments', params);
//         const resp = await instance.get<{ individual_appointments: any[] }>(
//           `/individual_appointments`,
//           { params }
//         );
//         console.log(
//           '[ClinikoClient] Response:', resp.status,
//           '→', (resp.data.individual_appointments || []).length, 'items'
//         );
//         return resp.data.individual_appointments || [];
//       } catch (err: any) {
//         console.error('[ClinikoClient][getAppointmentsRaw] Error:', err?.response?.data || err.message);
//         throw err;
//       }
//     },

//     async fetchResource<T>(url: string): Promise<T> {
//       try {
//         // Use explicit header object to avoid AxiosHeaders type issues
//         const resp = await axios.get<T>(
//           url,
//           {
//             headers: {
//               Authorization: instance.defaults.headers['Authorization'] as string,
//               'User-Agent': instance.defaults.headers['User-Agent'] as string,
//               Accept: instance.defaults.headers['Accept'] as string,
//             },
//             timeout: 15000,
//           }
//         );
//         // Return the full JSON payload
//         return resp.data;
//       } catch (err: any) {
//         console.error('[ClinikoClient][fetchResource] Error:', err?.response?.data || err.message);
//         console.error(err);
//         throw err;
//       }
//     },

//     async rescheduleAppointment(id: string, payload: {
//       starts_at: string;
//       ends_at?: string;
//       notes?: string;
//     }) {
//       try {
//         const response = await instance.patch(`/individual_appointments/${id}`, payload);
//         return response.data;
//       } catch (err: any) {
//         console.error('[ClinikoClient][rescheduleAppointment] Error:', err?.response?.data || err.message);
//         throw err;
//       }
//     },

//     async cancelAppointment(id: string, payload: {
//       cancellation_note?: string;
//       cancellation_reason: number;
//     }) {
//       try {
//         const response = await instance.patch(`/individual_appointments/${id}/cancel`, payload);
//         return response.data;
//       } catch (err: any) {
//         console.error('[ClinikoClient][cancelAppointment] Error:', err?.response?.data || err.message);
//         throw err;
//       }
//     },

//     async getAvailableSlots(params: {
//       business_id: string;
//       practitioner_id: string;
//       appointment_type_id: string;
//       from: string;
//       to: string;
//     }): Promise<string[]> {
//       const { business_id, practitioner_id, appointment_type_id, from, to } = params;
//       if (!appointment_type_id) {
//         throw new Error('appointment_type_id is required');
//       }
//       // Build the full URL as per Cliniko docs
//       const url = `${baseUrl}/businesses/${business_id}/practitioners/${practitioner_id}/appointment_types/${appointment_type_id}/available_times?from=${from}&to=${to}`;

//       try {
//         console.log('[ClinikoClient][getAvailableSlots] GET', url);
//         const resp = await axios.get(url, {
//           headers: {
//             Authorization: `Basic ${auth}`,
//             'User-Agent': 'SmartReceptionOutbound',
//             Accept: 'application/json',
//           },
//           timeout: 15000,
//         });

//         // return just the ISO strings
//         return resp.data.available_times.map((slot: any) => slot.appointment_start);
//       } catch (err: any) {
//         console.error(
//           '[ClinikoClient][getAvailableSlots] Error:',
//           err?.response?.data || err.message
//         );
//         throw err;
//       }
//     },
//   };
// }
