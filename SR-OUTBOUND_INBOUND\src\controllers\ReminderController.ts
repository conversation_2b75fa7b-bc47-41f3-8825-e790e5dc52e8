// import { AppointmentService } from '../services/AppointmentService';
// import { safeFormatDate } from '../utils/reminder';
// // import { callPatientWithRetell } from '../modules/retell';

// function toClinikoIso(date: Date): string {
//   return date.toISOString().replace(/\.\d{3}Z$/, 'Z');
// }

// export async function sendReminders(
//   svc: AppointmentService,
//   agentId: string,
//   fromNumber: string,
//   clinicName: string,
//   tz: string,
//   windowStart: Date,
//   windowEnd: Date,
//   clientId: string
// ): Promise<{ appointmentId: string; success: boolean; error?: string }[]> {
//   const startIso = toClinikoIso(windowStart);
//   const endIso = toClinikoIso(windowEnd);

//   console.log(`[sendReminders] Window in UTC: ${startIso} → ${endIso}`);

//   let appts = [];
//   try {
//     appts = await svc.getAppointments(startIso, endIso);
//     console.log(`[sendReminders] CRM returned ${appts.length} appts`);
//     console.dir(appts, { depth: null });
//   } catch (err: any) {
//     console.error(`[ERROR] fetching appointments:`, err);
//     return [];
//   }

//   const filtered = appts.filter((appt: any) => {
//     if (!appt || !appt.patient) {
//       console.warn('[SKIPPED] No patient:', appt);
//       return false;
//     }
//     const fallbackPhone = appt.patient.phone || '';
//     const isValid = fallbackPhone.startsWith('+') &&
//       appt.patient.fullName &&
//       appt.startsAt;
//     if (!isValid) {
//       console.warn(`[SKIPPED] Invalid appointment (id=${appt.id}):`, {
//         fullName: appt.patient.fullName,
//         startsAt: appt.startsAt,
//         phone: fallbackPhone,
//         raw: appt.patient
//       });
//     }
//     appt.patient.phone = fallbackPhone;
//     return isValid;
//   });

//   if (filtered.length === 0) {
//     console.warn(`[sendReminders] All appointments were skipped due to missing data.`);
//   }

//   const results = [];
//   for (const appt of filtered) {
//     try {
//       const formattedTime = safeFormatDate(appt.startsAt, tz);
//       const patientName = appt.patient.fullName;
//       const to = appt.patient.phone;
//       console.log(`[CALLING] To: ${to}, Name: ${patientName}, At: ${formattedTime}`);
//       // await callPatientWithRetell({
//       //   to,
//       //   agentId,
//       //   from: fromNumber,
//       //   variables: {
//       //     appointment_id: appt.id,
//       //     patient_name: patientName,
//       //     practitioner_name: appt.practitioner,
//       //     business_location_id: appt.business_id,
//       //     clientId,
//       //     appointment_time: formattedTime,
//       //     clinic_name: clinicName,
//       //   }
//       // });
//       console.log(`[retell response]`, JSON.stringify(results, null,2));
//       results.push({ appointmentId: appt.id, success: true });
//     } catch (e: any) {
//       console.error(`[ERROR] Failed for ${appt.id}:`, e.message);
//       results.push({ appointmentId: appt.id, success: false, error: e.message });
//     }
//   }
//   return results;
// }
import { callPatientWithRetell } from '../modules/retell';
import { safeFormatDate } from '../utils/reminder';

export interface Appointment {
  id: string;
  patient: { id: string; phone: string; fullName: string };
  startsAt: string;
  practitioner: string;
  practitioner_id: string;
  business_id: string;
}

export async function sendReminders(
  appointments: Appointment[],
  agentId: string,
  fromNumber: string,
  clinicName: string,
  tz: string,
  windowStart: Date,
  windowEnd: Date,
  clientId: string
) {
  for (const appt of appointments) {
    try {
      await callPatientWithRetell({
        to: appt.patient.phone,
        agentId,
        from: fromNumber,
        variables: {
          appointment_id: appt.id,
          patient_name: appt.patient.fullName,
          practitioner_name: appt.practitioner,
          clientId,
          appointment_time: safeFormatDate(appt.startsAt, tz),
          clinic_name: clinicName
        }
      });
    } catch (e) {
      console.error('Call failed for', appt.id, e);
    }
  }
}