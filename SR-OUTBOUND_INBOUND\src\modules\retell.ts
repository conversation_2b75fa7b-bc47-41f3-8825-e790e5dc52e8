// // src/modules/retell.ts
// import axios from 'axios';
// import dotenv from 'dotenv';
// dotenv.config();

// const RETELL_API_KEY = process.env.RETELL_API_KEY!;
// const RETELL_API_URL = 'https://api.retellai.com/v2/create-phone-call';

// if (!RETELL_API_KEY) {
//   throw new Error('RETELL_API_KEY missing from .env');
// }

// export type RetellDynamicVars = Record<string, string>;

// /**
//  * JSON schemas for our three new agent functions.
//  * These definitions tell the LLM how to call them.
//  */
// const AGENT_FUNCTIONS = [
//   {
//     name: "get_available_appointment_slots",
//     description:
//       "Get available appointment slots for a practitioner. from_date must be later than today, and to_date must be within the next 5 days.",
//     parameters: {
//       type: "object",
//       required: [
//         "practitioner_id",
//         "business_location_id",
//         "from_date",
//         "to_date",
//       ],
//       properties: {
//         practitioner_id: {
//           type: "string",
//           description: "The Cliniko practitioner ID",
//         },
//         business_location_id: {
//           type: "string",
//           description: "Your clinic's business_id in Cliniko",
//         },
//         from_date: {
//           type: "string",
//           description: "Start date in YYYY-MM-DD format",
//         },
//         to_date: {
//           type: "string",
//           description: "End date in YYYY-MM-DD format (≤ 5 days out)",
//         },
//       },
//     },
//   },
//   {
//     name: "reschedule_appointment",
//     description: "Reschedule an appointment.",
//     parameters: {
//       type: "object",
//       required: [
//         "appointment_id",
//         "new_appointment_starts_at",
//         "new_appointment_ends_at",
//       ],
//       properties: {
//         appointment_id: {
//           type: "string",
//           description: "The Cliniko ID of the appointment to reschedule",
//         },
//         new_appointment_starts_at: {
//           type: "string",
//           description: "New start datetime (ISO 8601)",
//         },
//         new_appointment_ends_at: {
//           type: "string",
//           description: "New end datetime (ISO 8601)",
//         },
//       },
//     },
//   },
//   {
//     name: "cancel_appointment",
//     description: "Cancel an appointment.",
//     parameters: {
//       type: "object",
//       required: ["appointment_id"],
//       properties: {
//         appointment_id: {
//           type: "string",
//           description: "The Cliniko ID of the appointment to cancel",
//         },
//         cancellation_reason: {
//           type: "integer",
//           description: "One of Cliniko’s cancellation_reason codes",
//         },
//         cancellation_note: {
//           type: "string",
//           description: "Free‑form cancellation note",
//         },
//       },
//     },
//   },
// ];

// export interface RetellCallInput {
//   to: string;               // Patient's phone number (E.164)
//   agentId: string;          // Retell agent ID
//   from: string;             // Your Twilio number
//   variables: RetellDynamicVars;  // dynamic variables for the prompt
// }

// /**
//  * Place an AI call via Retell, passing in our function definitions
//  * so the LLM can invoke rescheduling/cancellation/slot‑lookup.
//  */
// export async function callPatientWithRetell({
//   to,
//   agentId,
//   from,
//   variables,
// }: RetellCallInput): Promise<void> {
//   const payload = {
//     call_type: 'phone_call',
//     from_number: from,
//     to_number: to,
//     override_agent_id: agentId,
//     retell_llm_dynamic_variables: variables,
//     functions: AGENT_FUNCTIONS,
//     // Let the LLM immediately call a function if appropriate:
//     function_call: "auto"
//   };

//   try {
//     console.log('▶️ callPatientWithRetell payload:', JSON.stringify(payload, null, 2));
//     const response = await axios.post(RETELL_API_URL, payload, {
//       headers: {
//         Authorization: `Bearer ${RETELL_API_KEY}`,
//         'Content-Type': 'application/json',
//       },
//     });

//     console.log(
//       `[Retell] Call initiated to ${to} via agent ${agentId}`,
//       response.data
//     );
//   } catch (err: any) {
//     console.error(`[Retell] Failed to call ${to}:`, err.response?.data || err.message);
//     throw err;
//   }
// }
import axios from 'axios';
import 'dotenv/config';

const RETELL_API_KEY = process.env.RETELL_API_KEY!;
const RETELL_API_URL = 'https://api.retellai.com/v2/create-phone-call';

export async function callPatientWithRetell({
  to, agentId, from, variables
}: { to: string; agentId: string; from: string; variables: Record<string, string>; }) {
  await axios.post(RETELL_API_URL, {
    call_type: 'phone_call',
    from_number: from,
    to_number: to,
    override_agent_id: agentId,
    retell_llm_dynamic_variables: variables,
    functions: [
      { name: 'reschedule_appointment', description: '', parameters: { /* ... */ } },
      { name: 'cancel_appointment', description: '', parameters: { /* ... */ } },
      { name: 'get_available_appointment_slots', description: '', parameters: { /* ... */ } }
    ],
    function_call: 'auto'
  }, {
    headers: { Authorization: `Bearer ${RETELL_API_KEY}`, 'Content-Type': 'application/json' }
  });
}
