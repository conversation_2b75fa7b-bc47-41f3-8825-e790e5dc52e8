import { Schema, model, Document } from 'mongoose';

export interface ClientConfigDoc extends Document {
  name: string;
  crm: 'cliniko' | 'genie' | string;
  crmConfig: Record<string, any>;
  retellAgentId: string;
  twilioFromNumber: string;
  reminderHoursBefore: number;
  inboundEnabled: boolean;
  enabled: boolean;
}

const ClientConfigSchema = new Schema<ClientConfigDoc>({
  name: { type: String, required: true },
  crm: { type: String, required: true },
  crmConfig: { type: Schema.Types.Mixed, required: true },
  retellAgentId: { type: String, required: true },
  twilioFromNumber: { type: String, required: true },
  reminderHoursBefore: { type: Number, default: 24 },
  inboundEnabled: { type: Boolean, default: false },
  enabled: { type: Boolean, default: true },
});

export const ClientConfig = model<ClientConfigDoc>('ClientConfig', ClientConfigSchema);
