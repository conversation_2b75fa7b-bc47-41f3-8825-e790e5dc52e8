// import express from 'express';
// import mongoose from 'mongoose';
// import cron from 'node-cron';
// import dotenv from 'dotenv';
// import { runClientByName } from './modules/scheduler';
// // import { handleReschedule, handleCancel , handleGetAvailable } from './controllers/InboundController';
// // import agentRoutes from './routes/agent_routes';

// dotenv.config();

// (async function bootstrap() {
//   await mongoose.connect(process.env.MONGODB_URI!);
//   console.log('✅ MongoDB connected');

//   const isTest = process.env.TEST_MODE === 'true';
//   const clinicTz = process.env.TZ || 'Australia/Sydney';

//   // Client A – every day at 9:00am
//   cron.schedule(
//     '*/5 * * * *',
//     () => {
//       console.log('🔁 Cron: Running Client A');
//       runClientByName('A C T Neurology Centre', clinicTz, isTest);
//     },
//     { timezone: clinicTz }
//   );

//   // Client B – every day at 9:00am
//   cron.schedule(
//     '*/5 * * * *',
//     () => {
//       console.log('🔁 Cron: Running Client B');
//       runClientByName('Better Health Family Centre', clinicTz, isTest);
//     },
//     { timezone: clinicTz }
//   );

//   const app = express();
//   app.use(express.json());
//   // app.post('/api/agent/reschedule_appointment', async (req, res) => {
//   //   await handleReschedule(req.body, res);
//   // });
//   // app.post('/api/agent/get_available_slots', async (req, res) => {
//   //   await handleGetAvailable(req.body, res);
//   // });
//   // app.post('/api/agent/cancel_appointment', async (req, res)=>{
//   //   await handleCancel(req.body, res);
//   // });
//   // app.use('/api/agent', agentRoutes);

//   app.listen(3000, () => console.log('🚀 API listening on port 3000'));
// })();

import express from 'express';
import mongoose from 'mongoose';
import cron from 'node-cron';
import dotenv from 'dotenv';
import { runClientByName } from './modules/scheduler';

dotenv.config();

(async () => {
  await mongoose.connect(process.env.MONGODB_URI!);
  const app = express();
  app.use(express.json());

  const tz = process.env.TZ || 'Australia/Sydney';

  cron.schedule('0 9 * * *', () => runClientByName('Client A', tz), { timezone: tz });
  cron.schedule('0 9 * * *', () => runClientByName('Client B', tz), { timezone: tz });

  const port = process.env.PORT || 3000;
  app.listen(port, () => console.log(`Outbound service listening on ${port}`));
})();
